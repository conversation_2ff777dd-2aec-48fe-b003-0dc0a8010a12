let SimpleDevelopPlugin = require('@simplex/simple-develop');

module.exports = function (env, argv) {
    return {
        module: {
            rules: [
                {
                    test: /\.js$/,
                    exclude: /(node_modules|bower_components)/,
                    use: 'eslint-loader',
                    enforce: 'pre'
                },
                {
                    test: /\.js$/,
                    exclude: /(node_modules|bower_components)/,
                    use: 'babel-loader'
                }
            ]
        },
        plugins: [
            new SimpleDevelopPlugin(__dirname, argv)
        ]
    }
}