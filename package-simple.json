{"develop": false, "hosts": {"client": {"demo": "./"}, "server": {}}, "dir": {"src": "./src/", "web": "./web/", "node": "./node/", "weixin": "./weixin/"}, "router": [{"name": "index", "app": "./router/home", "route": "/"}, {"name": "coach", "app": "./router/coach", "route": "/coach.html"}, {"name": "rewardRule", "app": "./router/rewardRule", "route": "/rewardRule.html"}, {"name": "studentShare", "app": "./router/studentShare", "route": "/studentShare.html"}, {"name": "errorPage", "app": "./router/errorPage", "route": "/errorPage.html"}], "bundle": {"zip": true, "dir": "../"}, "server": {"hotDev": true, "port": 2190, "timeout": 10000, "cluster": true, "index": "./index.html", "resourcePath": "/", "log": {"kafka": {"host": "************", "port": 9092, "topic": "nodejs-log", "partition": 0}}}, "head": {"meta": [{"charset": "UTF-8"}, {"name": "viewport", "content": "width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,viewport-fit=cover"}, {"http-equiv": "X-UA-Compatible", "content": "IE=Edge,chrome=1"}, {"name": "renderer", "content": "webkit"}, {"name": "format-detection", "content": "telephone=no, email=no"}]}, "build": {"style": {"remUnit": 100, "baseWidth": 750, "mobileMaxWidth": 500}, "server": ["server"], "static": ["static"]}}