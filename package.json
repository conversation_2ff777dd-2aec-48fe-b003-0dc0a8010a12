{"name": "jiaolianbaodian-invite-students", "version": "1.0.3", "description": "", "dependencies": {"@babel/runtime": "7.13.9", "@babel/runtime-corejs3": "7.13.10", "@simplex/simple-node": "1.0.2", "@simplex/simple-core": "4.0.16", "@simplex/simple-base": "6.9.2", "@simplex/simple-mcprotocol": "2.3.3", "@simplex/simple-oort": "4.3.4", "@simplex/simple-base-sso": "2.0.4", "@simplex/simple-mock": "1.0.1", "html2canvas": "1.4.1"}, "devDependencies": {"@babel/core": "^7.13.8", "@babel/eslint-parser": "^7.13.14", "@babel/plugin-proposal-class-properties": "^7.13.0", "@babel/plugin-proposal-decorators": "^7.13.5", "@babel/plugin-transform-runtime": "^7.13.9", "@babel/preset-env": "^7.13.9", "@simplex/eslint-config-simple": "4.0.0", "@simplex-types/simple-core": "1.1.9", "@simplex/simple-develop": "4.1.29", "babel-loader": "^8.2.2", "babel-plugin-transform-decorators-legacy": "^1.3.5", "eslint": "^7.24.0", "eslint-loader": "^4.0.2"}, "engines": {"node": ">=12.16.0", "npm": ">=7.9.0"}, "eslintConfig": {"extends": "@simplex/eslint-config-simple"}, "scripts": {"webdev": "npx webpack --config ./webpack/webpack.web.config.js --mode development --progress --watch", "webdev:dev": "npx webpack --config ./webpack/webpack.web.config.js --mode development --progress --watch --env package.simple=package-simple.dev.json", "webdev:test": "npx webpack --config ./webpack/webpack.web.config.js --mode development --progress --watch --env package.simple=package-simple.test.json", "bundler": "npx webpack --config ./webpack/webpack.web.config.js --mode production", "bundler:dev": "npx webpack --config ./webpack/webpack.web.config.js --mode production --env package.simple=package-simple.dev.json", "bundler:test": "npx webpack --config ./webpack/webpack.web.config.js --mode production --env package.simple=package-simple.test.json"}, "private": true, "simple": "./package-simple.json"}