import Simple from '@simplex/simple-core';
import Initialize from ':initialize/main';

export default class {

    constructor() {

        let that = this;

        // 初始化
        that.runInitialize();

        that.Router = Simple.Router.create();

        that.router();

    }

    runInitialize() {
        Initialize();
    }

    router() {

        let that = this;

        that.Router.use('*', (params, context) => {
            that.route(params, context);
        });

    }

    go() {
        this.Router.go(location.href);
    }

}