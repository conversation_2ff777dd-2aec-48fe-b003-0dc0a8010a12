/* eslint-disable operator-assignment */
/* eslint-disable no-redeclare */
/* eslint-disable no-sequences */
/* eslint-disable no-return-assign */
/* eslint-disable consistent-this */
/* eslint-disable one-var */
/* eslint-disable no-unused-expressions */
/* eslint-disable vars-on-top */
/* eslint-disable wrap-regex */
/* eslint-disable quote-props */
export default {
  
    formatDate: function (dateValue, formatStr) {
        var timestamp = dateValue;
        var fmt = formatStr;
        var o;
        var k;
        var week = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];

        if (!dateValue) {
            return '';
        }

        if (typeof timestamp !== 'object') {
            timestamp = new Date(timestamp);
        }
        fmt = fmt || 'yyyy-MM-dd';

        o = {
            'M+': timestamp.getMonth() + 1,
            'd+': timestamp.getDate(),
            'h+': timestamp.getHours() % 12,
            'H+': timestamp.getHours(),
            'm+': timestamp.getMinutes(),
            's+': timestamp.getSeconds(),
            'q+': Math.floor((timestamp.getMonth() + 3) / 3),
            S: timestamp.getMilliseconds(),
            'W+': week[timestamp.getDay()]
        };

        if (/(y+)/.test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (timestamp.getFullYear() + '').substr(4 - RegExp.$1.length));
        }
        for (k in o) {
            if (new RegExp('(' + k + ')').test(fmt)) {
                // eslint-disable-next-line eqeqeq
                fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
            }
        }

        return fmt;
    },
    _r: function (a) {
        var c = Math.abs(parseInt(new Date().getTime() * Math.random() * 10000)).toString();
        var d = 0;
        var b;
        var e;

        for (b = 0; b < c.length; b++) {
            d += parseInt(c[b]);
        }
        e = (function (f) {
            return function (g, h) {
                return h - '' + g.length <= 0 ? g : (f[h] || (f[h] = Array(h + 1).join(0))) + g;
            };
        })([]);
        d += c.length;
        d = e(d, 3 - d.toString().length);

        return a.toString() + c + d;
    },

    objToParams: function (obj) {
        var params = [];

        for (var o in obj) {
            params.push(o + '=' + encodeURIComponent(obj[o]));
        }
        return params.join('&');
    },

    loadingHide: function () {
        var $loading = document.getElementById('loading');

        if ($loading) {
            $loading.classList.remove('is-show');
        }
    },

    loadingShow: function () {
        var $loading = document.getElementById('loading');

        if (!$loading) {
            $loading = document.createElement('div');
            $loading.id = 'loading';
            $loading.classList.add('loading');
            $loading.innerHTML = '<p class="con">正在拼命加载中...</p>';
            document.body.appendChild($loading);
        }

        $loading.classList.add('is-show');
    },
    getURLParams: function (url) {
        var params = {};

        (url || window.location.href).replace(/[#|?&]+([^=#|&]+)=([^#|&]*)/gi, function (m, key, value) {
            params[key] = decodeURIComponent(value);
        });

        return params;
    },
    loadJs(url = [], cb) {
        const promiseArr = [];

        if (typeof url === 'string') {
            url = [url];
        } else if (!(url instanceof Array)) {
            throw new TypeError('参数为url,或url数组');
        }

        for (let i = 0, len = url.length; i < len; i++) {
            // eslint-disable-next-line no-new
            promiseArr.push(
                new Promise((resolve) => {
                    const script = document.createElement('script');
                    script.src = url[i];
                    document.head.appendChild(script);
                    script.onload = function () {
                        document.head.removeChild(script);
                        resolve(true);
                    };
                })
            );
        }

        cb &&
            Promise.all(promiseArr).then((value) => {
                cb();
            });
    },
    toast(message, time, cb) {
        var div = document.createElement('div');

        if (document.getElementById('myToast')) {
            document.body.removeChild(document.getElementById('myToast'));
        }

        div.innerText = message;

        div.setAttribute('id', 'myToast');

        div.style.position = 'fixed';
        div.style.left = '50%';
        div.style.top = '50%';
        div.style.transform = 'translate(-50%, -50%)';
        div.style.webkitTransform = 'translate(-50%, -50%)';
        div.style.background = 'rgba(0, 0, 0, 0.7)';
        div.style.padding = '10px 20px';
        div.style.borderRadius = '6px';
        div.style.textAlign = 'center';
        div.style.color = '#ffffff';
        div.style.maxWidth = '90%';
        div.style.minWidth = '60%';
        div.style.fontSize = '0.3rem';
        div.style.lineHeight = '1.5';
        div.style.zIndex = '99998565135416846';

        time = time || 2000;
        document.body.appendChild(div);
        setTimeout(function () {
            try {
                document.body.removeChild(div);
                cb && cb();
            } catch (e) {
                cb && cb();
            }
        }, time);
    },
    debounce: function (e, t, n) {
        var i,
            a,
            r,
            s,
            o,
            l = function () {
                var c = Date.now() - s;
                c < t && c >= 0
                    ? (i = setTimeout(l, t - c))
                    : ((i = null), n || ((o = e.apply(r, a)), i || (r = a = null)));
            };
        return function () {
            (r = this), (a = arguments), (s = Date.now());
            var c = n && !i;
            return i || (i = setTimeout(l, t)), c && ((o = e.apply(r, a)), (r = a = null)), o;
        };
    },

    formatDistance: function (d, m) {
        var distance = d || undefined;
        var m = m || '米';
    
        if (typeof d !== 'number') {
            return undefined;
        }
    
        if (distance && distance > 1000) {
            distance = (distance / 1000).toFixed(1) + '公里';
        } else if (distance && distance > 0 && distance < 1000) {
            distance = distance + m;
        } else {
            distance = '';
        }
    
        return distance;
    }
};
