import utils from '../utils/index';
import Interface from './baseStore';

export default function (config, params) {
    return new Promise((resolve, reject) => {
        const paramsObj = {
            ...params
        };

        if (Object.prototype.hasOwnProperty.call(paramsObj, '_androidId')) {
            Reflect.deleteProperty(paramsObj, '_androidId');
        }
        Interface(config)
            .request({
                ...paramsObj
            })
            .done((data, res) => {
                console.log('data: ', config, params, data, res);
                resolve(data);
            })
            .fail((...err) => {
                console.log('error: ', err);
                utils.loadingHide();
                utils.toast(err[1].data ? err[1].data.message : '网络超时');
                reject(err);
            });
    });
}
