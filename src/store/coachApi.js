/* eslint-disable func-style */
import Interface from './create';

export function getToken(params) {
    return Interface({
        hostName: 'volvo',
        url: 'api/open/file/acquire-token.htm',
        method: 'GET',
        basicParams: false,
        params,
        sign: '*#06#cJ08mqedhpJIbnyKfZyQpJNu'
    });
}

export function getCoachBaseInfo(params) {
    return Interface({
        hostName: 'santana',
        url: 'api/web/v4/coach/base-info.htm',
        method: 'POST',
        basicParams: false,
        params
    });
}
