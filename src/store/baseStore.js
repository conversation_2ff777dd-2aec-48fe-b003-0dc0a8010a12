import { MCBaseStore } from '@simplex/simple-base';
import Simple from '@simplex/simple-core';

export default function (config) {
    // eslint-disable-next-line no-underscore-dangle
    const _config = {};
    let sign;

    _config.ajaxOptions = {
        timeout: 50000
    };

    const hostName = config.hostName || 'interface';

    if ('hostName' in config) {
        Reflect.deleteProperty(config, 'hostName');
    }
    if ('sign' in config) {
        sign = config.sign;
        Reflect.deleteProperty(config, 'sign');
    }
    for (const i in config) {
        if (i === 'url') {
            _config[i] = Simple.Store.getHosts()[hostName] + config[i];
        } else if (i === 'allUrl') {
            _config.url = config[i];
        } else if (config[i] instanceof Object) {
            _config[i] = {
                ...(_config[i] || {}),
                ...config[i]
            };
        } else {
            _config[i] = config[i];
        }
    }

    return MCBaseStore.extend(_config).create({
        sign
    });
}
