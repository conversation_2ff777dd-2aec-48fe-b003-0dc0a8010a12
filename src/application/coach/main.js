/*
 * main
 *
 * name: xia<PERSON><PERSON>a
 * date: 16/3/24
 */

import { Application } from '@simplex/simple-core';
import { MCProtocol, OORT, MCBaseUtils, MCBaseUI, MCShare } from '@simplex/simple-base';

import Server from './server';
import View from './view/main.html';
//  saveImg
import { openWeb } from ':src/common/core';
import { getToken, getCoachBaseInfo } from ':src/store/coachApi';
import html2canvas from 'html2canvas';

const title = '邀请学员';
const eventPage = '邀请学员';
const eventGroup = 'h5';
const UASTR = window.navigator.userAgent || '';
export default class extends Application {
    isHarmony = !!UASTR.match(/Harmony/gi);
    $constructor(params, context) {

        let that = this;

        that.$super({
            target: document.body,
            server: Server,
            name: module.id,
            view: View
        });

        that.params = params;
        that.context = context;

        that.props = {};

        that.state = {
            isLogin: false,
            authToken: that.params.search.authToken || '',
            coachName: '',
            jiaxiaoName: '',
            avatar: '',
            isAndroid: MCBaseUtils.isAndroid
        };

    }

    didMount() {
        const that = this;
        document.title = title
        MCProtocol.Core.Web.setting({
            titleBar: false,
            fullScreen: true
        });

        OORT.logEvt(eventPage, `${eventPage}页面展示`, eventGroup)
        if (MCBaseUtils.isInApp) {
            MCProtocol.Core.User.get((userInfo) => {
                // 如果有返回值 则已登录
                console.log(userInfo, 111111)
                if (userInfo.success) {
                    
                    // 更新登录状态
                    that.setState({
                        isLogin: true,
                        authToken: userInfo.data.authToken
                        // avatar: userInfo.data.avatar,
                        // coachName: that.state.coachName || userInfo.data.nickname
                    });
                    that.qrcode();
                    that.queryBaseInfo();
                } else {
                    that.login()
                }
            })
        } else if (that.state.authToken) {
            that.qrcode();
            that.queryBaseInfo();
        } 
        
    }

    login() {
        const that = this;
        MCProtocol.Core.User.login({
            pageType: 'sms',
            skipAuthRealName: true,
            from: 'jiakaobaodian',
            callback: function (data) {
                console.log('login', data)
                that.setState({
                    isLogin: true,
                    authToken: data.authToken
                });
                that.qrcode();
                that.queryBaseInfo();
            }
        });
    }
    /**
     * 返回
     * */
    goBack() {
        MCProtocol.Core.Web.back();
    }
    /**
     * 跳转记录详情
     * */ 
    goRecord() {
        // https://share-m.kakamobi.com/activity.kakamobi.com/jiaolianbaodian-xyyq/detail.html?flag=true
        OORT.logEvt(eventPage, `${eventPage}-推荐记录-按钮-点击`, eventGroup);
        openWeb({
            url: 'https://share-m.kakamobi.com/activity.kakamobi.com/jiaolianbaodian-xyyq/detail.html?flag=true'
        });
    }
    /**
     * 跳转活动规则
     * */
    goRegular() {
        openWeb({
            url: window.location.origin + window.location.pathname.replace(/\/[^/]*$/ig, '') + '/rewardRule.html'
        });
    }
    /**
     * 跳转认证
     * */
    goCertification() {
        location.href = 'http://jiaxiao.nav.mucang.cn/coach/indentify-authenticate';
    }

    saveQrcodeCard() {
        const that = this;
        const node = this.getDOMNode().qrCodeCard;
        this.setState({
            saving: true
        });
        OORT.logEvt(eventPage, '邀请学员-保存二维码点击', eventGroup);
        
        if (that.state.getBase64Url) {
            html2canvas(node, {
                useCORS: true,
                background: 'transparent'
            }).then(canvas => {
                var src = canvas.toDataURL(); 
                // saveImg(src)
                MCBaseUI.toast('正在保存图片', 1500);
                setTimeout(() => {
                    // MCBaseUtils.saveImgInApp(src)
                    that.saveImgInApp(src)
                }, 1500)
            });
        } else {
            that.qrcode();
            MCBaseUI.toast('网络出错了啦，请重试！');
        } 
    }

    shareMenu(e) {
        const that = this;
        const { type } = e.refTarget.dataset;
        OORT.logEvt(eventPage, type === 'moment' ? '邀请学员-朋友圈点击' : '邀请学员-微信好友点击', eventGroup);
        this.setState({
            saving: true
        });
        const node = this.getDOMNode().qrCodeCard;
        setTimeout(() => {
            html2canvas(node, {
                useCORS: true,
                background: 'transparent'
            }).then(canvas => {
                var src = canvas.toDataURL(); 
                MCBaseUI.loading();
                that.uploadWeb(src, function (previewUrl) {
                    MCBaseUI.loadingHide();
                    let shareUrl = previewUrl;
                    const shareObj = {
                        imageUrl: shareUrl,
                        shareForceBigImage: 1
                    }
                    if (MCBaseUtils.isInApp) {
                        MCBaseUI.toast('正在跳转到微信', 1500);
                        setTimeout(() => {
                            MCShare.open(`weixin_${type}`, shareObj, 'qichebaojiazhijia-coach-rank', 'image')
                        }, 1500)
                    } 
                });
            });
        }, 50)
    }
    uploadWeb(file, cb) {
        getToken().then((res) => {
            MCProtocol.Core.Native.uploadImage2(
                {   
                    data: file,
                    upToken: res.value,
                    // 默认volvo
                    upAppKey: '581a536c62d04958925966e31d24297c'
                },
                (data) => {
                    cb(data.data.previewUrl)
                }
            );
        }).catch(err => {
            MCBaseUI.loadingHide();
            alert('获取失败：文件未上传成功');
        })
    }
    imgParseBase64(url, cb) {
        var image = new Image();
        image.src = url;
        image.setAttribute('crossOrigin', 'anonymous');

        image.onload = function () {
            const canvas = document.createElement('canvas');
            canvas.width = image.width;
            canvas.height = image.height;
            let ctx = canvas.getContext('2d');
            ctx.drawImage(image, 0, 0, image.width, image.height);
            let dataURL = canvas.toDataURL('image/png');
            cb(dataURL)
        };
    }
    qrcode(flag) {
        let that = this;
        // authToken=9836f19cc0411ab77d51c01136c098b71ca220b0&_r=17347129013101876076
        let url = 'https://santana.kakamobi.cn/api/web/v4/promotion/qr-code.htm?authToken=' + this.state.authToken + '&_r=';
        if (MCBaseUtils.isDev) {
            url = 'https://santana.ttt.mucang.cn/api/web/v4/promotion/qr-code.htm?authToken=' + this.state.authToken + '&_r=';
        }
        this.state.scanURl = url + MCBaseUtils.getRandomR(1);
        // 避免重复请求
        setTimeout(() => {
            this.imgParseBase64(url + MCBaseUtils.getRandomR(1), (src) => {
                if (src) {
                    that.setState({
                        getBase64Url: true,
                        scanURl: src
                    });
                    
                }
            })
        }, 20);
    }
    queryBaseInfo() {
        const that = this;
        getCoachBaseInfo({
            authToken: this.state.authToken,
            _r: MCBaseUtils.getRandomR(1)
        }).then((res) => {
            console.log(res, 444)
            that.setState({
                avatar: res.avatar,
                coachName: res.name,
                jiaxiaoName: res.jiaxiaoName
            });
        }).catch(err => {
            console.log('base info', err)
        })
    }

    checkSavePermission(cb) {
        var pName = 'android.permission.WRITE_EXTERNAL_STORAGE';

        MCProtocol.Core.Permission.check({
            permission: pName,
            callback: function (cdata) {
                if (cdata && cdata.success) {
                    cb && cb();
                } else {
                    MCProtocol.Core.Permission.request({
                        permission: pName,
                        callback: function (rdata) {
                            if (rdata && rdata.success) {
                                cb && cb();
                            } else {
                                MCBaseUI.loadingHide();
                                MCBaseUI.toast('请开启存储权限');
                            }
                        }
                    });
                }
            }
        });
    }

    saveImgInApp(data, cb) {
        var that = this;

        if (!MCBaseUtils.isInApp) {
            return false;
        }

        MCBaseUI.loading();

        MCBaseUtils.getAppBaseParams(function (baseParams) {
            if (MCBaseUtils.isAndroid && baseParams._systemVersion <= 9 && MCBaseUtils.hasMCFeature('core.permission.check')) {
                that.checkSavePermission(function () {
                    MCProtocol.Core.Native.saveImage({
                        data: data,
                        callback: function (res) {
                            MCBaseUI.loadingHide();

                            if (res && res.success) {
                                MCBaseUI.toast('已保存到相册');
                                cb && cb();
                            } else {
                                MCBaseUI.toast('保存失败，请开启存储权限');
                            }
                        }
                    });
                });
            } else {
                MCProtocol.Core.Native.saveImage({
                    data: data,
                    callback: function (res) {
                        MCBaseUI.loadingHide();

                        if (res && res.success) {
                            MCBaseUI.toast('已保存到相册');
                            cb && cb();
                        } else {
                            MCBaseUI.toast('保存失败，请开启存储权限');
                        }
                    }
                });
            }
        })

        return true;
    }
}