/*
 * main
 *
 * name: xia<PERSON><PERSON>a
 * date: 16/3/24
 */
body {
    padding: 0;
    margin: 0;
}
.ft500 {
    font-weight: bold;
    font-size: 32px;
}
.fz14 {
    font-size: 28px;
}
.mb40 {
    margin-bottom: 40px;
}

.coach {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    .top {
      width: 100%;
      height: 80px;
      background: #fff;
    }
    .header {
        width: 100%;
        height: 88px;
        background: #fff;
        line-height: 88px;
        text-align: center;
        box-sizing: border-box;
        padding: 0 44px;
        text-align: center;
        position: relative;
        // margin-top: constant(safe-area-inset-top);
        // margin-top: env(safe-area-inset-top);

        div:nth-child(1) {
            position: absolute;
            top: 0;
            left: 44px;
            width: 40px;
            height: 100%;
            background: url('./images/back.png') no-repeat center center;
            background-size: 100%;
        }
        .title {
            color: #333333;
            font-size: 36px
        }
        .record {
            position: absolute;
            top: 0;
            right: 44px;
            color: #333333;
            font-size: 34px;
        }
    }
    .container {
      flex: 1;
      overflow-y: scroll;
      -webkit-overflow-scrolling: touch;

        img {
            width: 100%;
        }
        .content {
            position: relative;
            .con {
                padding: 30px;
    
            .title {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
                a {
                    font-size: 26px;
                    font-weight: normal;
                    color: #1dacf9;
                    text-decoration: underline;
                }
            }

            .rewardRule {
                position: relative;
                display: flex;
                align-items: center;
                height: 2px;
                width: 550px;
                background: rgba(24,144,255,.3);
                margin: 30px auto;
                li {
                    width: 136px;
                    height: 1px;
                    display: flex;
                    align-items: center;
                i {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    background: #1890FF;
                    text-align: center;
                    font-size: 20px;
                    color: #fff;
                    line-height: 32px;
                }
                }
                li:nth-of-type(2) {
                    width: 300px;
                    height: 1px;
                    justify-content: center;
                } 
                li:first-child {
                    justify-content:flex-start;
                }  
                li:last-child {
                    justify-content:flex-end;
                }
            } 
            .rewardRuleText {
                display: flex;
                align-items: center;
                margin: 0 auto;
                justify-content: center;
                width: 100%;
                li {
                    width: 200px;
                    font-size: 28px;
                    color: #333;
                    line-height: 40px;
                    text-align: center;
                }
                li:nth-of-type(1) {
                    padding-right: 24px;
                }
                li:nth-of-type(2) {
                    width: 280px;
                }
                li:nth-of-type(3) {
                    padding-right: 0;
                }
            }    
            }
            .myQrcode{
                padding: 30px;
                .qrcodeBox{
                    img{
                        display: block;
                        height: 300px;
                        width: 300px;
                        margin: 0 auto;
                    }
                }
                .saveQrcode{
                    width: 240px;
                    height: 72px;
                    background: linear-gradient(270deg, #3F8EFF 0%, #26C3FD 100%);
                    border-radius: 36px;
                    text-align: center;
                    line-height: 72px;
                    color: #fff;
                    margin: 30px auto 0px;
                    cursor: pointer;
                    font-size: 30px;
                }
                .qrTips {
                    margin-top: 36px;
                    width: 100%;
                    color: #999999;
                    font-size: 24px;
                    line-height: 34px;
                    text-align: center;
                }
            }
            .stub {
                width: 100%;
                height: 20px;
                background-color: #f1f4f7;
            }
            .introduce {
                padding: 40px 30px 340px;
                font-size: 0;
                .introduceTitle {
                    width: 604px;
                    height: 36px;
                    margin: 20px auto 60px;
                    background: url(./images/title.png) center center no-repeat;
                    background-size: 100% auto;
                }
                p,a {
                    display: inline-block;
                    font-size: 28px;
                    width: 80%;
                    margin-left: 20%;
                    text-align: center;
                }
                a {
                    margin-top: 10px;
                    text-decoration: underline;
                }
    
            }
            .goHuodong{
                font-size: 28px;
                color: #00A0F4;
                text-decoration: underline;
            }
            .bottom {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: #fff;
                .title {
                    padding-left: 30px;
                    font-size: 40px;
                    margin-bottom: 30px;
                    font-weight: bold;
                    margin-top: 30px;
                }
                .bot-b {
                    display: flex;
                    padding: 30px 0;
                    div {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                    img {
                        width: 104px;
                        height: 104px;
                        margin-bottom: 20px;
                    }
                    p {
                        font-size: 24px;
                        color: #666666;
                        text-align: center;
                    }
                    }
                }
            }
      }
    }
  }
  .qrcodeCard{
    position: fixed;
    top:100000px;
    width:750px;
    height: 1180px;
    box-sizing: border-box;
    .codeCardBg {
        width:750px;
        height: 1180px;
    }
    .avatar{
      position: absolute;
      top: 60px;
      width: 100%;
      img{
        display: block;
        width: 160px;
        height: 160px;
        border-radius: 50%;
        border: 2px solid #fff;
        margin: 0 auto;
        overflow: hidden;
      }
    }
    .infoBox {
      position: absolute;
      top: 240px;
      width: 100%;
      font-size: 36px;
      color: #333;
      line-height: 50px;
      text-align: center;
    }
    .qrcodeBox {
      position: absolute;
      bottom: 190px;
      width: 100%;
      img {
        margin: 0 auto;
        display: block;
        width: 280px;
        height: 280px;
        border: 2px solid #d3eaff;
      }
    }
  
  }
  .home.iphoneX {
    padding-top: 48px;
  }