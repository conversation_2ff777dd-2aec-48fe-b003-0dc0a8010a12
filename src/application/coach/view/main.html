<import name="style" content="../../../common/main.css" />
<import name="style" content="./main" module="S" />

<div class=":coach" >
    <div class=":top"></div>
    <div class=":header">
        <div class=":iconBack" sp-on:click="goBack"></div>
        <div class=":title">邀请学员</div>
        <div class=":record" sp-on:click="goRecord">推荐记录</div>
    </div>
    <div class=":container">
        <div class=":content">
          <div class=":con">
            <p class=":title :ft500">邀请奖励
                <a sp-on:click="goRegular">活动规则</a>
            </p>
            <p class=":fz14 :mb40">1元+学员驾考宝典购买VIP返利</p>
            
            <p class=":ft500">邀请攻略</p>
            <ul class=":rewardRule">
              <li>
                <i>1</i>
              </li>
              <li>
                <i>2</i>
              </li>
              <li>
                <i>3</i>
              </li>
            </ul>
            <ul class=":rewardRuleText">
              <li>学员微信扫码</li>
              <li>学员打开驾考宝典登录</li>
              <li>发放奖励</li>
            </ul>
          </div>
          <div class=":myQrcode">
            <!-- <p class="ft500">我的二维码</p> -->
            <div class=":qrcodeBox">
              <img class=":scanUrl" src="{{state.scanURl}}" alt />
            </div>
            <div class=":saveQrcode" sp-on:click="saveQrcodeCard">保存二维码</div>
            <p class=":qrTips">温馨提示：邀请的学员必须历史没有注册登录过</p>
          </div>
          <div class=":stub"></div>
          <div class=":introduce">
            <div class=":introduceTitle">
              <!-- <img src="static/coach//title.png" /> -->
            </div>
            <img src="static/coach//one.png" />
            <img src="static/coach//two.png" />
            <img src="static/coach//three.png" />
            <p>提示：完成认证后，学员提成现金才能提现</p>
            <a class=":goHuodong" sp-on:click="goCertification">立即前往认证</a>
          </div>
          <div class=":stub"></div>
          <div class=":bottom :share-fixed">
            <div class=":title">分享推荐</div>
            <div class=":bot-b">
              <div sp-on:click="shareMenu" data-type="friend">
                <img src="static/coach/wx2.png" />
                <p>微信好友</p>
              </div>

              <sp:if value="{{!self.isHarmony}}">
                <div sp-on:click="shareMenu" data-type="moment">
                  <img src="static/coach/pyq.png" />
                  <p>朋友圈</p>
                </div>
              </sp:if>
              <div sp-on:click="saveQrcodeCard">
                <img src="static/coach/saveQr.png" />
                <p>保存二维码</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class=":qrcodeCard" ref="qrCodeCard" sp-if="{{state.saving}}" id="qrcodeCard">
        <img class=":codeCardBg" src="static/coach/QrcodeCard.png" alt="">
        <div class=":avatar">
          <img src="{{state.avatar}}" crossorigin="anonymous" alt />
        </div>
          <div class=":infoBox">
          {{ state.coachName }} <span>|</span> {{ state.jiaxiaoName }}
        </div>
        <div class=":qrcodeBox">
          <img class=":scan-url" src="{{state.scanURl}}" />
        </div>
      </div>
</div>