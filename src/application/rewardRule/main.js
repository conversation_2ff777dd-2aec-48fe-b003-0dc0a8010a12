/*
 * main
 *
 * name: xia<PERSON>jia
 * date: 16/3/24
 */

import { Application } from '@simplex/simple-core';
import { MCProtocol, OORT } from '@simplex/simple-base';

import Server from './server';
import View from './view/main.html';

const title = '奖励规则';
const eventPage = '邀请学员';
// const eventGroup = 'h5';
export default class extends Application {

    $constructor(params, context) {

        let that = this;

        that.$super({
            target: document.body,
            server: Server,
            name: module.id,
            view: View
        });

        that.params = params;
        that.context = context;

        that.props = {};

        that.state = {
            isLogin: false,
            authToken: ''
        };

    }

    didMount() {
        // eslint-disable-next-line no-unused-vars
        const that = this;
        document.title = title
        MCProtocol.Core.Web.setting({
            titleBar: true,
            title: title,
            button: '',
            menu: false
        });

        OORT.logEvt(eventPage, `${eventPage}页面展示`, 'jiaxiaozhijia')

    }
}