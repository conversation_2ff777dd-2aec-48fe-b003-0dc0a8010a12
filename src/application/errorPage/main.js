/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */

import { Application } from '@simplex/simple-core';
import { MCProtocol } from '@simplex/simple-base';

import Server from './server';
import View from './view/main.html';

export default class extends Application {

    $constructor(params, context) {

        let that = this;

        that.$super({
            target: document.body,
            server: Server,
            name: module.id,
            view: View
        });

        that.params = params;
        that.context = context;

        that.props = {};

        that.state = {
        };

    }

    didMount() {
        document.title = '数据迁移中'
        MCProtocol.Core.Web.setting({
            titleBar: true,
            title: '数据迁移中',
            button: '',
            menu: false
        });

    }
}