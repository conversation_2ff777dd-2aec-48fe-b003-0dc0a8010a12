/*
 * main
 *
 * name: xia<PERSON>ji<PERSON>
 * date: 16/3/24
 */
body {
    padding: 0;
    margin: 0;
}
.home {
    position: relative;
    max-width: 750px;
    img {
        display: block;
        width: 100%;
        height: auto;
        margin: 0 auto;
    }
    .btnCopy {
        position: absolute;
        bottom: 500px;
        right: 116px;
        z-index: 1;
        width: 91px;
        height: 36px;
        background: linear-gradient(#e53927 0%, #e95b39 100%);
        border-radius: 8px;
        font-weight: 400;
        text-align: center;
        color: #fefefe;
        font-size: 24px;
        line-height: 36px;
        &::before{
            content: '';
            position: absolute;
            top: -20px;
            right: -20px;
            bottom: -20px;
            left: -20px;
        }
    }
    .btnSub {
        position: fixed;
        bottom: 20px;
        left: 50%;
        z-index: 1;
        width: 600px;
        height: 147px;
        background: url(https://web-resource.mc-cdn.cn/web/jiaxiao/e6b266a0-abe3-11ec-94b8-131050842435.png) center bottom no-repeat;
        background-size: 100% auto;
        transform: translateX(-300px);
        // margin: -190px auto 0;
    }
}