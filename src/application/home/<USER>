/*
 * main
 *
 * name: xia<PERSON><PERSON><PERSON>
 * date: 16/3/24
 */

import { Application } from '@simplex/simple-core';
import { MCProtocol, OORT} from '@simplex/simple-base';

import util from ':src/utils/index';
import Server from './server';
import View from './view/main.html';
export default class extends Application {

    $constructor(params, context) {

        let that = this;

        that.$super({
            target: document.body,
            server: Server,
            name: module.id,
            view: View
        });

        that.params = params;
        that.context = context;

        that.props = {};

        that.state = {
            count: 10
        };

    }

    didMount() {
        // eslint-disable-next-line no-unused-vars
        const that = this;
        document.title = '全科VIP免费送'
        MCProtocol.Core.Web.setting({
            titleBar: true,
            title: '全科VIP免费送',
            button: '',
            menu: false
        });

        OORT.logEvt('全科VIP免费送-H5', '全科VIP免费送页面展示', 'jiaxiaozhijia')
        // MCProtocol.Core.User.get((userInfo) => {
        //     // 如果有返回值 则已登录
        //     if (userInfo.success) {
        //         that.setState({
        //             isLogin: true,
        //             authToken: userInfo.data.authToken
        //         });
        //     }
        // })
        // that.bindEvent();
    }
    goYQxy() {
        const isDev = !!window.location.href.match(/m.ttt.|localhost|192.168|127.0.0.1|172.20/gi);
        if (isDev) {
            location.href = 'https://share-m.ttt.kakamobi.com/activity.kakamobi.com/jiaolianbaodian-recommend/#/index';
        } else {
            location.href = 'https://share-m.kakamobi.com/activity.kakamobi.com/jiaolianbaodian-recommend/#/index';
        }
        
    }
    copy() {
        const input = document.createElement('input');
        input.setAttribute('readonly', 'readonly');
        input.setAttribute('value', 'jiakaobaodian107');
        document.body.appendChild(input);
        input.select();
        input.setSelectionRange(0, 9999);
        if (document.execCommand('copy')) {
            document.execCommand('copy');
        }
        document.body.removeChild(input);
        util.toast('文字已复制');
    }

}