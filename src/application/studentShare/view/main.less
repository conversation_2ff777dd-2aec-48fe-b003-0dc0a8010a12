/*
 * main
 *
 * name: xia<PERSON><PERSON><PERSON>
 * date: 16/3/24
 */
body {
    padding: 0;
    margin: 0;
    background-color: #F4F7F7;
}
.studentShare {
    padding: 24px 30px;
    background-color: #F4F7F7;

    .f14 {
        font-size: 28px;
        font-weight: 500;
    }
    .h2 {
        font-size: 36px;
        color: #333;
        font-weight: bold;
    }
    .flex {
        display: flex;
        align-items: center;
        justify-content: space-between;

        h2 {
            flex: 1;
        }
        
    }
    
    .poster {
        display: block;
        width: 100%;
        height: auto;
        margin: 16px auto 36px;
    }

    .link {
        display: flex;
        align-items: center;
        justify-content: center;

        .icon {
            margin-left: 18px;
            width: 16px;
            height: 28px;
            background: url('./image/icon_arrow.png') center/90% no-repeat;
        }
    }
}