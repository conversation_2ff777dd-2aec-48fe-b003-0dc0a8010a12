/*
 * main
 *
 * name: xia<PERSON><PERSON>a
 * date: 16/3/24
 */

import { Application } from '@simplex/simple-core';
import { MCProtocol, OORT} from '@simplex/simple-base';

import Server from './server';
import View from './view/main.html';
export default class extends Application {

    $constructor(params, context) {

        let that = this;

        that.$super({
            target: document.body,
            server: Server,
            name: module.id,
            view: View
        });

        that.params = params;
        that.context = context;

        that.props = {};

        that.state = {
            count: 10
        };

    }

    didMount() {
        // eslint-disable-next-line no-unused-vars
        const that = this;
        document.title = '学员分享教练'
        MCProtocol.Core.Web.setting({
            titleBar: true,
            title: '学员分享教练',
            button: '',
            menu: false
        });

        OORT.logEvt('学员分享教练-H5', '学员分享教练页面展示', 'h5')

    }
    goWelfare() {
        
        location.href = 'http://jiaxiao.nav.mucang.cn/explore?index=3';

    }

}