import { Store, Utils } from '@simplex/simple-core';

let SetBaseWidth = () => {

    let clientWidth = window.screen.width;
    let baseFontSize;

    if (clientWidth < 1200) {
        baseFontSize = 100 * (clientWidth / Package.build.style.baseWidth);
    } else {
        baseFontSize = 100;
    }

    document.documentElement.style.fontSize = baseFontSize + 'px';

}

export default () => {

    if (Utils.platform.browser) {
        // 设置hosts
        Store.setHosts(Package.hosts);
        const isDev = !!window.location.href.match(/m.ttt.|localhost|192.168|127.0.0.1|172.20/gi);

        if (isDev) {
            // 测试hosts
            Store.setHosts({
                santana: 'https://santana.ttt.mucang.cn/',
                volvo: 'https://volvo-jiaxiao.ttt.mucang.cn/'
            });
        } else {
            // 线上hosts
            Store.setHosts({
                santana: 'https://santana.kakamobi.cn/',
                volvo: 'https://volvo-jiaxiao.kakamobi.cn/'
            });
        }
        // 设置页面基础宽度
        SetBaseWidth();

    }

};