import { MCProtocol, MCBaseUtils, MCBaseUI } from '@simplex/simple-base';

export const openWeb = function (config) {
    const { url, title } = config;

    if (MCBaseUtils.isInApp) {
        MCProtocol.Core.Web.open({
            url: url,
            titleBar: true,
            toolbar: true,
            title: title,
            menu: false,
            button: false,
            orientation: 'portrait'
        });
    } else {
        window.location.href = url;
    }
}

// MCProtocol.register('core.permission.check', function (config, cb) {
//     return {
//         config,
//         callback: function (data) {
//             cb && cb(data);
//         }
//     };
// });

// MCProtocol.register('core.permission.request', function (config, cb) {
//     return {
//         config,
//         callback: function (data) {
//             cb && cb(data);
//         }
//     };
// });

MCProtocol.register('Core.Native.uploadImage2', function (config, callback) {
    return {
        config,
        callback: function (data) {
            if (typeof data === 'object') {
                callback((data));
            } else if (typeof data === 'string') {
                callback(JSON.parse(data));
            }
        }
    }
});

export const shareWeixinImageSettings = function (imageUrl) {
    MCProtocol.Core.Share.setting({
        needServer: true,
        shareEvent: 'jiakaobaodian',
        type: 'image',
        shareData: {
            imageUrl
        }
    });

}

export const shareWeixinImage = function (imageUrl) {
    return new Promise(resolve => {
        MCProtocol.Core.Share.open({
            channel: 'weixin_friend',
            type: 'image',
            shareData: { imageUrl },
            callback() {
                resolve();
            }
        });
    });
}

export const checkPermission = function (cb) {
    var pName = 'android.permission.READ_EXTERNAL_STORAGE';

    MCProtocol.Core.Permission.check({
        permission: pName,
        callback: function (cdata) {
            if (cdata && cdata.success) {
                cb && cb();
            } else {
                MCProtocol.Core.Permission.request({
                    permission: pName,
                    callback: function (rdata) {
                        if (rdata && rdata.success) {
                            cb && cb();
                        } else {
                            // 兼容 安卓sdk 版本号>=33
                            MCProtocol.Core.Permission.request({
                                permission: 'android.permission.READ_MEDIA_IMAGES',
                                callback: function (mdata) {
                                    if (mdata.success) {
                                        cb && cb();
                                    } else {
                                        MCBaseUI.loadingHide();
                                        MCBaseUI.toast('请开启存储权限');
                                    }
                                }
                            });
                        }
                    }
                });
            }
        }
    });
}

export const saveImg = function (data, cb) {
    MCBaseUI.toast('正在保存图片', 1500);
    setTimeout(() => {
        MCBaseUI.loading();
        if (MCBaseUtils.isIOS) {
            MCProtocol.Core.Native.saveImage({
                data: data, 
                callback: function (res) {
                    MCBaseUI.loadingHide();
                    if (res.success) {
                        MCBaseUI.toast('已保存到相册');
                        cb && cb();
                    } else {
                        MCBaseUI.toast('保存失败，请开启存储权限');
                    }
                }
            });
        } else {
            checkPermission(function () {
                MCProtocol.Core.Native.saveImage({
                    data: data, 
                    callback: function (res) {
                        MCBaseUI.loadingHide();
                        if (res.success) {
                            MCBaseUI.toast('已保存到相册');
                            cb && cb();
                        } else {
                            MCBaseUI.toast('保存失败，请开启存储权限');
                        }
                    }
                });
            });
        } 
    }, 1500)
}