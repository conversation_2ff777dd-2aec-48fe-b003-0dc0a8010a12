image: registry-vpc.cn-hangzhou.aliyuncs.com/mc-public/node:14.17.3
stages:
  - build

before_script:
 - cp /frontEndScripts/init.sh /init.sh
 - chmod +x /init.sh

build_pack:
  stage: build
  variables:
     BUILD_ART_PATH: build/dist/web
     BUILD_CMD: |-
       master: npm run bundler
       dev*: npm run bundler:dev
       test*: npm run bundler:test
       hotfix*: npm run bundler:test
  tags:
  - k8s-front
  artifacts:
    expire_in: 7 days
    name: "$CI_PROJECT_NAME-$CI_COMMIT_BRANCH"
    paths:
    - project.zip
  script:
  - /init.sh 